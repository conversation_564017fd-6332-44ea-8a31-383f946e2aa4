import 'package:auto_route/annotations.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fusion/extension/list.dart';
import 'package:fusion/gen/app.gaps.dart';
import 'package:fusion/gen/assets.gen.dart';
import 'package:fusion/gen/colors.gen.dart';
import 'package:fusion/pages/order/controller/order_detail_controller.dart';
import 'package:fusion/router/app_router.dart';
import 'package:fusion/store/user_store.dart';
import 'package:fusion/utils/constants.dart';
import 'package:fusion/widgets/app_bar_back_icon.dart';
import 'package:fusion/widgets/app_progress_indicator.dart';
import 'package:fusion/widgets/bottom_tool_bar.dart';
import 'package:fusion/widgets/count_down.dart';
import 'package:fusion/widgets/extension_on_widget_iterable.dart';
import 'package:fusion/widgets/gen_image_extension.dart';
import 'package:fusion/widgets/state_page/state_page_builder.dart';
import 'package:fusion/widgets/steam_guard_level.dart';
import 'package:fusion/widgets/text_filter/modal_text_filter_dialog.dart';
import 'package:fusion/widgets/text_filter/text_filter_option_data.dart';
import 'package:fusion/widgets/toast.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import 'widget/order_accessory_card.dart';
import 'widget/order_item_data.dart';

@RoutePage<bool>()
class OrderDetailPage extends StatefulWidget {
  /// 查询的订单id
  final String orderId;

  const OrderDetailPage({super.key, @PathParam('id') required this.orderId});

  @override
  State<OrderDetailPage> createState() => _OrderDetailPageState();
}

class _OrderDetailPageState extends State<OrderDetailPage> {
  final moreMenuKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrderDetailController>(
      init: OrderDetailController(orderId: widget.orderId),
      builder: _buildBody,
    );
  }

  var numberFormat = NumberFormat('#.##');
  void _cancelOrder(OrderDetailController controller) async {
    if (controller.data.value?.isBuyer == true) {
      final confirm = await AppRouter.to.push(
        MessageDialogRoute(
          title: "确认取消订单？",
          message: "如果订单因为报价取消未成功导致订单取消失败时，您可以自行在Steam中取消报价！",
        ),
      );
      if (confirm != true) {
        return;
      }
    } else {
      final confirm = await AppRouter.to.push(
        MessageDialogRoute(
          title: "确认取消报价？",
          message: "如果报价取消未成功，您可以自行在Steam中取消报价！",
        ),
      );
      if (confirm != true) {
        return;
      }
    }
    bool login = await UserStore.to.whenSteamLogin(useLoading: true);
    if (!login) return;
    final result = await controller.cancel();
    if (result.isSuccess() && mounted) {
      AppToast.success(context, "取消成功");
    }
    controller.onRefresh();
  }

  void _showMoreMenu(OrderDetailController controller) async {
    final ret = await showDialog(
      context: context,
      useSafeArea: false,
      routeSettings: const RouteSettings(name: "OrderDetailMoreMenuDialog"),
      builder: (c) => ModalTextFilterDialog<int>(
        alignment: Alignment.topRight,
        anchor: moreMenuKey,
        options: [
          if (controller.enableCancel)
            TextFilterOptionData(
              value: 1,
              title: "取消订单",
              highlight: false,
            ),
          if (controller.enableFeedback)
            TextFilterOptionData(
              value: 2,
              title: "问题反馈",
              highlight: false,
            ),
        ],
      ),
    );
    if (ret == null) {
      return;
    }
    switch (ret) {
      case 1:
        _cancelOrder(controller);
        break;
      case 2:
        AppRouter.to.push(const FeedbackRoute());
      default:
        return;
    }
  }

  Widget _buildBody(OrderDetailController controller) {
    return StatePageBuilder(
      state: controller.pageState,
      enableScaffold: true,
      builder: (ctx) {
        return Scaffold(
          appBar: AppBar(
            leading: const AppBarBackIcon(),
            title: Text("${controller.data.value?.type.name}订单详情"),
            actions: [
              if (controller.enableCancel || controller.enableFeedback)
                IconButton(
                  key: moreMenuKey,
                  padding: const EdgeInsets.symmetric(
                    horizontal: Constants.horizontalPadding,
                    vertical: Constants.contentPadding / 2,
                  ),
                  onPressed: () {
                    _showMoreMenu(controller);
                  },
                  icon: Assets.icon.more.toWidget(
                    size: 16,
                    color: Colors.black,
                  ),
                ),
            ],
          ),
          body: ListView(
            padding: const EdgeInsets.symmetric(
              horizontal: Constants.horizontalPadding / 2,
            ),
            children: [
              _buildStateBar(controller),
              if (controller.needsPendingNotice) ...[
                _buildPendingNotice(),
                Gaps.v12,
              ],
              _buildSectionBox([
                _buildSectionHeader("订单信息"),
                if (controller.data.value?.isBuyer == true)
                  _buildBaseInfoRow(
                    "卖家",
                    controller.data.value?.partnerName ?? "",
                    useTopMargin: false,
                  )
                else
                  _buildBaseInfoRow(
                    "买家",
                    controller.data.value?.partnerName ?? "",
                    useTopMargin: false,
                  ),
                _buildBaseInfoRow("订单编号", controller.data.value?.id ?? "",
                    rightAction: Container(
                      margin: const EdgeInsets.only(left: 4),
                      child: const Icon(
                        Icons.copy,
                        size: 12,
                      ),
                    ), onTap: () {
                  Clipboard.setData(ClipboardData(text: controller.orderId));
                  AppToast.success(ctx, "已复制订单id");
                }),
                _buildBaseInfoRow(
                  "下单时间",
                  controller.data.value?.createTime ?? "",
                ),
                if (controller.data.value?.isBuyer == true)
                  _buildBaseInfoRow("支付方式",
                      controller.data.value?.paymentMethod.getName() ?? ""),
                if (controller.data.value?.isBuyer != true) ...[
                  _buildBaseInfoRow(
                    '出售价格',
                    "¥${numberFormat.format(controller.data.value?.base.unitPrice)}",
                  ),
                  _buildBaseInfoRow(
                    '手续费',
                    "¥${numberFormat.format(controller.data.value?.base.serviceAmount)}",
                  ),
                ],
                _buildBaseInfoRow(
                  controller.priceTitle,
                  controller.data.value?.price ?? "",
                  rightColor: AppColors.redEC292D,
                  rightBold: true,
                ),
              ]),
              if (controller.needsRefundCard) ...[
                Gaps.v12,
                _buildRefundCard(controller),
              ],
              if (controller.needsBuyerGuard) ...[
                Gaps.v12,
                _buildGuard(ctx, controller),
              ],
              Gaps.v12,
              _buildGoodsCard(controller),
              Gaps.v12,
              if (controller.data.value?.isBuyer == true)
                _buildNoticeForBuyer()
              else
                _buildNoticeForSeller(),
            ],
          ),
          bottomNavigationBar:
              controller.data.value?.actions?.isNotEmpty == true
                  ? _buildToolBar(controller)
                  : null,
        );
      },
    );
  }

  Widget _buildRefundCard(OrderDetailController controller) {
    return _buildSectionBox([
      _buildSectionHeader("退款信息"),
      _buildBaseInfoRow(
        "退款金额",
        controller.data.value?.price ?? "",
        rightColor: AppColors.redEC292D,
        rightBold: true,
        useTopMargin: false,
      ),
      if (controller.paymentStatus == 2) ...[
        _buildBaseInfoRow(
          "退款方式",
          "${controller.data.value?.paymentMethod.getName()}原路退回",
        ),
        _buildBaseInfoRow(
          "退款时间",
          controller.refundTime,
        ),
      ],
    ]);
  }

  Widget _buildPendingNotice() {
    return _buildSectionBox(
      [
        const Text(
          "暂挂订单一般48小时会自动完成， 建议双方等待这个暂挂期结束，不要主动取消这个交易，否则会触发Steam的7天交易冷却安全限制！",
          style: TextStyle(
            color: AppColors.redEC292D,
            fontSize: 12,
          ),
        ),
      ],
      enableTopPadding: true,
    );
  }

  Widget _buildToolBar(OrderDetailController controller) {
    return BottomToolBar(
      child: Row(
        children: [
          Gaps.h16,
          ...?controller.data.value?.actions
              ?.map(
                (e) => Expanded(
                  child: FilledButton(
                    onPressed: () {
                      switch (e) {
                        case OrderAction.createTradeOffer:
                          AppRouter.to
                              .push(const BuyWaitingSendTradeOfferTodoRoute());
                          return;
                        case OrderAction.delivery:
                          AppRouter.to
                              .push(const SellWaitingDeliveryTodoRoute());
                          return;
                        case OrderAction.acceptTradeOffer:
                          AppRouter.to.push(const BuyWaitingReceiveTodoRoute());
                          return;
                        case OrderAction.tradeProtection:
                          AppRouter.to.push(const BuyWaitingReceiveTodoRoute());
                        case OrderAction.pauseWant:
                        case OrderAction.cancelWant:
                        case OrderAction.resumeWant:
                        case OrderAction.changeSellingPrice:
                        case OrderAction.pay:
                          return;
                      }
                    },
                    child: Text(e.actionText),
                  ),
                ),
              )
              .spaceBetween(10, direction: Axis.horizontal),
          Gaps.h16,
        ],
      ),
    );
  }

  Widget _buildGuard(BuildContext context, OrderDetailController controller) {
    final guard = controller.guardInfo;
    if (guard == null) {
      return const SizedBox.shrink();
    }
    return _buildSectionBox([
      _buildSectionHeader(
        "买家Steam令牌",
        padding: EdgeInsets.zero,
        action: ObxValue(
          (running) => GestureDetector(
            onTap: running.value
                ? null
                : () async {
                    running.value = true;
                    final res = await controller.onRefreshGuard();
                    running.value = false;
                    res.fold(
                      (success) => AppToast.success(context, "刷新令牌成功"),
                      (failure) => AppToast.error(context, failure),
                    );
                  },
            child: Container(
              padding: const EdgeInsets.only(
                top: Constants.contentPadding,
                bottom: Constants.contentPadding,
                left: Constants.contentPadding,
              ),
              child: running.value
                  ? const AppProgressIndicator(
                      size: 16,
                    )
                  : const Icon(
                      Icons.refresh,
                      size: 16,
                    ),
            ),
          ),
          false.obs,
        ),
      ),
      Container(
        padding: const EdgeInsets.all(
          Constants.horizontalPadding / 2,
        ),
        decoration: BoxDecoration(
          color: AppColors.steamGuardBg,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          children: [
            // if (guard.avatar?.isNotEmpty == true) ...[
            //   CachedNetworkImage(
            //     imageUrl: guard.avatar ?? "",
            //     width: 48,
            //     height: 48,
            //   ),
            //   Gaps.h12,
            // ],
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  guard.username?.isNotEmpty == true ? guard.username! : "--",
                  style: const TextStyle(
                    color: Colors.white,
                  ),
                ),
                Text(
                  "加入Steam：${guard.createTime?.split(" ")[0]}",
                  style: const TextStyle(
                    color: AppColors.steamGuardTimeText,
                    fontSize: 12,
                  ),
                )
              ],
            ),
            const Spacer(),
            if (guard.level != null) SteamGuardLevel(data: guard),
            if (guard.levelIcon?.isNotEmpty == true)
              SteamGuardLevelImage(data: guard)
          ],
        ),
      ),
      Gaps.v12,
      const Text(
        "确认报价时请注意核实对方的Steam令牌信息，谨防欺诈！如果令牌信息有差异，请刷新后再次确认",
        style: TextStyle(
          color: AppColors.orange,
          fontSize: 12,
        ),
      )
    ]);
  }

  Widget _buildNoticeForBuyer() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: Constants.horizontalPadding / 2,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          "卖家需在12小时内发货，发货超时系统将自动取消订单",
          "如需取消订单，请在付款30分钟后在Steam取消报价，否则将被视为恶意购买，恶意购买将会受到封禁购买功能的惩罚",
          "请勿自行在Steam处理报价，请勿接受卖家修改后的Steam交易报价"
        ]
            .mapWithIndex(
              (e, i) => Text(
                "${i + 1}.$e",
                style: const TextStyle(
                    fontSize: 12, color: AppColors.gray, height: 1.6),
              ),
            )
            .spaceBetween(4, direction: Axis.vertical)
            .toList(growable: false),
      ),
    );
  }

  Widget _buildNoticeForSeller() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: Constants.horizontalPadding / 2,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          "在确认报价时，请仔细核对买家的Steam令牌，谨防欺诈！",
          "如需取消订单，请在报价发起半个小时后取消对应报价",
        ]
            .mapWithIndex(
              (e, i) => Text(
                "${i + 1}.$e",
                style: const TextStyle(
                    fontSize: 12, color: AppColors.gray, height: 1.6),
              ),
            )
            .spaceBetween(4, direction: Axis.vertical)
            .toList(growable: false),
      ),
    );
  }

  Widget _buildGoodsCard(OrderDetailController controller) {
    return _buildSectionBox([
      _buildSectionHeader("${controller.data.value?.type.name}饰品"),
      ...?controller.data.value?.accessories.mapWithIndex(
        (e, i) => GestureDetector(
          onTap: () {
            final state = controller.data.value?.state;
            AppRouter.to.push(GoodsSellingDetailRoute(
                orderId: state == OrderState.failure
                    ? ''
                    : controller.data.value!.id,
                sellingId: controller.data.value!.base.sellingId.toString(),
                invId: controller.data.value!.base.invId.toString(),
                goodsId: controller.data.value!.info.goodsId!.toString()));
          },
          // 订单详情 挂件
          child: OrderDetailAccessoryCard(
            // enablePrice: controller.data.value?.type == OrderType.want,
            data: e,
            padding: i != 0
                ? const EdgeInsets.only(
                    top: Constants.contentPadding,
                  )
                : null,
          ),
        ),
      ),
    ]);
  }

  Widget _buildBaseInfoRow(String left, String right,
      {Color? rightColor,
      bool rightBold = false,
      bool useTopMargin = true,
      Widget? rightAction,
      VoidCallback? onTap}) {
    final widget = Container(
      margin: useTopMargin
          ? const EdgeInsets.only(
              top: Constants.horizontalPadding,
            )
          : null,
      child: Row(
        children: [
          Text(
            left,
            style: const TextStyle(color: AppColors.gray, fontSize: 14),
          ),
          Gaps.h16,
          Expanded(
            child: Text(
              right,
              textAlign: TextAlign.end,
              style: TextStyle(
                color: rightColor ?? AppColors.black,
                fontSize: 14,
                fontWeight: rightBold ? FontWeight.bold : null,
              ),
            ),
          ),
          if (rightAction != null) rightAction,
        ],
      ),
    );
    if (onTap == null) {
      return widget;
    }
    return GestureDetector(
      onTap: onTap,
      child: widget,
    );
  }

  Widget _buildSectionHeader(String title,
      {Widget? action,
      EdgeInsetsGeometry padding = const EdgeInsets.symmetric(
        vertical: Constants.contentPadding,
      )}) {
    Widget widget = Text(
      title,
      style: const TextStyle(
        fontSize: 16,
        color: AppColors.black,
        fontWeight: FontWeight.bold,
      ),
    );
    if (action != null) {
      widget = Row(
        children: [widget, const Spacer(), action],
      );
    }
    widget = Container(
      padding: padding,
      child: widget,
    );
    return widget;
  }

  Widget _buildSectionBox(List<Widget> children,
      {bool enableTopPadding = false}) {
    return Container(
      padding: EdgeInsets.only(
        top: enableTopPadding ? Constants.horizontalPadding : 0,
        left: Constants.horizontalPadding / 2,
        right: Constants.horizontalPadding / 2,
        bottom: Constants.horizontalPadding,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }

  Widget _buildStateBar(OrderDetailController controller) {
    Widget widget = Row(
      children: [
        Text(
          controller.stateMessage,
          style: TextStyle(
            color: controller.data.value?.stateMessageColor,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        if (controller.countdown > 0)
          Countdown(
            key: ValueKey("${controller.orderId}-${controller.countdown}"),
            builder: (ctx, count, str) {
              return Text(
                str,
                style: TextStyle(
                  color: controller.data.value?.stateMessageColor,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              );
            },
            times: controller.countdown,
            onExpired: controller.onRefresh,
          ),
      ],
    );
    if (controller.data.value?.stateReason?.isNotEmpty == true) {
      widget = Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          widget,
          Gaps.v12,
          Text(
            controller.data.value?.stateReason ?? "",
            style: TextStyle(
              fontSize: 14,
              color: controller.data.value?.stateReasonColor,
            ),
          )
        ],
      );
    }
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: Constants.horizontalPadding / 2,
        vertical: 20,
      ),
      child: widget,
    );
  }
}
