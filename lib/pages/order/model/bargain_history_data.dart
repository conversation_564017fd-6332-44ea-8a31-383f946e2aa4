import 'package:fusion/pages/order/widget/order_item_data.dart';
import 'package:fusion/pages/sell/model/bargain_item_data.dart';
import 'package:fusion/utils/accessory_tag_parser.dart';
import 'package:fusion/utils/payment_method.dart';
import 'package:fusion/widgets/accessory_tag.dart';
import 'package:openapi/openapi.dart';

import 'order_sticker.dart';

class BargainHistoryData extends OrderItemData {
  final BffInterfaceBargainList200ResponseDataItemsInner selling;
  final V1orderGoodsInfo goods;

  BargainHistoryData(this.selling, this.goods);

  @override
  String? get buyerUid => "";

  @override
  String? get wear => selling.wear;

  @override
  String? get styleName => selling.styleName ?? '';

  @override
  List<OrderAction>? get actions => null;

  @override
  List<String> get coverUrls => [goods.item?.goodsInfo?.iconUrl ?? ""];

  @override
  bool get displayPaymentMethod => false;

  @override
  bool get displayTopBar => true;

  @override
  bool get enableActions => false;

  @override
  double get price =>
      selling.originalPrice != null ? selling.originalPrice! * 1.0 : 0.0;

  @override
  double? get expiredPrice =>
      selling.originalPrice != null ? selling.originalPrice! * 1.0 : 0.0;

  @override
  int get finishedCount => 1;

  @override
  String get name => goods.item?.goodsInfo?.marketName ?? "";

  @override
  String? get stateMessage {
    /// 任何一个成功了，就成功
    if (selling.bargainInfos?.any((p0) => p0.bargainState == 5) == true) {
      return "成功";
    }

    return "待处理";
  }

  bool get pending =>
      selling.bargainInfos
          ?.any((p0) => BargainState.fromInt(p0.bargainState).pending) ==
      true;

  /// 还价历史无具体的倒计时，倒计时在还价条目上
  @override
  int get orderStatusCountdown => 0;

  @override
  PaymentMethod get paymentMethod => PaymentMethod.alipay;

  @override
  String? get priceTip => null;

  @override
  int get requiredCount => 1;

  @override
  List<OrderSticker>? get stickers => null;

  @override
  String? get topDateTime => null;

  @override
  OrderType get type => OrderType.none;

  @override
  bool get useInlineStateMessage => false;

  @override
  List<AccessoryTagData>? get coverTags =>
      parseCoverTags(goods.item?.goodsInfo?.tags);

  @override
  String get id => selling.sellingId ?? "NEEDS_SELLING_ID";
}
