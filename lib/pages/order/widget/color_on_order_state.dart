import 'dart:ui';

import 'package:fusion/gen/colors.gen.dart';

import 'order_item_data.dart';

extension Colorx on OrderState {
  /// 文字颜色
  Color get textColor {
    switch (this) {
      case OrderState.none:
      case OrderState.waitingConfirmTradeOffer:
      case OrderState.waitingPay:
      case OrderState.waitingCreateTradeOffer:
      case OrderState.waitingResponseTradeOffer:
      case OrderState.waitingReceiveTradeOffer:
      case OrderState.tradeOfferPending:
       case OrderState.exception:
       case OrderState.tradeProtection:
        return AppColors.orange;
      case OrderState.failure:
        return AppColors.red;
      case OrderState.success:
        return AppColors.green;
    }
  }
}
