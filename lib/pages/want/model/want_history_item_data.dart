import 'dart:ui';

import 'package:fusion/extension/double.dart';
import 'package:fusion/pages/order/widget/order_item_data.dart';
import 'package:fusion/pages/want/model/want_status.dart';
import 'package:fusion/utils/accessory_tag_parser.dart';
import 'package:fusion/utils/payment_method.dart';
import 'package:fusion/widgets/accessory_tag.dart';
import 'package:openapi/openapi.dart';

import '../../order/model/order_sticker.dart';

class WantHistoryItemData extends OrderItemData {
  final BffInterfacePurchaseOrderList200ResponseDataItemsInner order;
  final V1GoodsListInfoMin goods;

  /// 外部配置的是否运行动作按钮。优先级最高
  final bool enableAction;

  WantHistoryItemData(this.order, this.goods, this.enableAction);

  @override
  String get buyerUid => order.uid ?? "";

  @override
  String? get wear => null;

  String? get wearInterval => order.wearInterval;

  @override
  String? get styleName => order.styleName;

  @override
  List<OrderAction>? get actions {
    if (enableAction == false) {
      return null;
    }
    switch (WantStatus.fromInt(order.purchaseState)) {
      case WantStatus.none:
      case WantStatus.waitingPay:
      case WantStatus.cancelled:
      case WantStatus.success:
        return null;
      case WantStatus.processing:
        return [OrderAction.cancelWant, OrderAction.pauseWant];
      case WantStatus.paused:
        return [OrderAction.cancelWant, OrderAction.resumeWant];
    }
  }

  @override
  List<String> get coverUrls => [goods.goodsInfo?.iconUrl ?? ""];

  @override
  bool get displayPaymentMethod => true;

  @override
  bool get displayTopBar => true;

  @override
  double? get expiredPrice => null;

  @override
  int get finishedCount => order.receivedCount ?? 0;

  @override
  String get name => goods.goodsInfo?.marketName ?? "";

  @override
  String? get stateMessage => order.purchaseStateMsg;

  @override
  Color get stateMessageColor {
    return WantStatus.fromInt(order.purchaseState).textColor;
  }

  @override
  int get orderStatusCountdown => order.countdown ?? 0;

  @override
  PaymentMethod get paymentMethod => PaymentMethod.fromInt(order.payType);

  @override
  String? get paymentMethodName =>
      paymentMethod.getName(mapper: {PaymentMethod.balance: "余额0元求购"});

  @override
  double get price =>
      order.purchaseUnitPrice != null ? order.purchaseUnitPrice! * 1.0 : 0;

  @override
  String? get priceTip {
    if (enableAction == false) {
      return null;
    }
    final max = order.purchaseMaxPrice;
    if (max == null) {
      return null;
    }
    return "现最高¥${max == 0.0 ? '--' : max.removeTrailingZero()}";
  }

  @override
  int get requiredCount => order.buyCount ?? 1;

  @override
  List<OrderSticker>? get stickers => null;

  @override
  String? get topDateTime => order.purchaseTime;

  @override
  OrderType get type => OrderType.want;

  @override
  bool get useInlineActions => false;

  @override
  bool get enableActions => enableAction;

  @override
  bool get useInlineStateMessage => false;

  @override
  List<AccessoryTagData>? get coverTags =>
      parseCoverTags(goods.goodsInfo?.tags);

  @override
  String get id => order.purchaseId ?? "NEEDS__ID";
}
