import 'package:fusion/pages/order/widget/order_item_data.dart';
import 'package:fusion/utils/accessory_tag_parser.dart';
import 'package:fusion/utils/payment_method.dart';
import 'package:fusion/widgets/accessory_tag.dart';
import 'package:openapi/openapi.dart';

import 'order_sticker.dart';

class BargainResolveData extends OrderItemData {
  final BffInterfaceBargainList200ResponseDataItemsInner selling;
  final V1orderGoodsInfo goods;
  final List<OrderSticker>? stickersInfo;
  BargainResolveData(this.selling, this.goods, this.stickersInfo);

  @override
  String? get buyerUid => "";

  @override
  String? get wear => selling.wear;

  @override
  String? get styleName => selling.styleName ?? '';

  @override
  List<OrderAction>? get actions => [OrderAction.changeSellingPrice];

  @override
  List<String> get coverUrls => [goods.item?.goodsInfo?.iconUrl ?? ""];

  @override
  bool get displayPaymentMethod => false;

  @override
  bool get displayTopBar => false;

  @override
  bool get enableActions => true;

  @override
  double? get expiredPrice => null;

  @override
  int get finishedCount => 1;

  @override
  String get name => goods.item?.goodsInfo?.marketName ?? "";

  @override
  int get orderStatusCountdown => 0;

  @override
  PaymentMethod get paymentMethod => PaymentMethod.alipay;

  @override
  double get price => selling.originalPrice ?? 0.0;

  @override
  String? get priceTip => null;

  @override
  int get requiredCount => 1;

  @override
  String? get stateMessage => null;

  @override
  List<OrderSticker>? get stickers => stickersInfo;

  @override
  String? get topDateTime => null;

  /// 未生成订单
  @override
  OrderType get type => OrderType.none;

  @override
  bool get useInlineStateMessage => false;

  @override
  bool get useInlineActions => true;

  @override
  List<AccessoryTagData>? get coverTags =>
      parseCoverTags(goods.item?.goodsInfo?.tags);

  @override
  String get id => selling.sellingId ?? "NEEDS_SELLING_ID";
}
