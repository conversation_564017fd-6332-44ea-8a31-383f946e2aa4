import 'package:fusion/gen/colors.gen.dart';
import 'package:fusion/pages/order/model/order_history_item_data.dart';
import 'package:fusion/pages/order/widget/order_item_data.dart';
import 'package:flutter/painting.dart';
import 'package:fusion/pages/order/widget/color_on_order_state.dart';

class SellHistoryItemData extends OrderHistoryItemData {
  SellHistoryItemData(super.order, super.goods, super.stickers);

  @override
  bool get usePendingIcon => true;

  @override
  Color get stateMessageColor {
    switch (state) {
      case OrderState.waitingCreateTradeOffer:
        if ((tradeInitiator == TradeInitiator.seller && isBuyer) ||
            (tradeInitiator == TradeInitiator.buyer && !isBuyer)) {
          return AppColors.blue206CFE;
        }
        return OrderState.fromInt(order.state).textColor;
      case OrderState.waitingConfirmTradeOffer:
        if (isBuyer) {
          return AppColors.blue206CFE;
        }
        return OrderState.fromInt(order.state).textColor;
      case OrderState.waitingResponseTradeOffer:
      case OrderState.waitingReceiveTradeOffer:
        if ((tradeInitiator == TradeInitiator.buyer && isBuyer) ||
            (tradeInitiator == TradeInitiator.seller && !isBuyer)) {
          return AppColors.blue206CFE;
        }
        return OrderState.fromInt(order.state).textColor;
      default:
        return OrderState.fromInt(order.state).textColor;
    }
  }

  @override
  bool get useInlineActions =>
      enableActions &&
      !(actions != null && actions!.length > 1) &&
      state != OrderState.tradeProtection;
}
