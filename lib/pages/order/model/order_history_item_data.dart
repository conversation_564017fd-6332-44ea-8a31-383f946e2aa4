import 'package:fusion/pages/order/widget/order_item_data.dart';
import 'package:fusion/store/user_store.dart';
import 'package:fusion/utils/accessory_tag_parser.dart';
import 'package:fusion/utils/payment_method.dart';
import 'package:fusion/widgets/accessory_tag.dart';
import 'package:openapi/openapi.dart';

import 'order_sticker.dart';

/// 订单数据模型
class OrderHistoryItemData extends OrderItemData {
  final V1OrderOnly order;
  final V1GoodsListInfoMin goods;

  OrderHistoryItemData(
    this.order,
    this.goods,
    this.stickers,
  );

  @override
  String get buyerUid => order.buyerUid ?? "";

  @override
  String? get wear => order.wear;

  @override
  String? get styleName => order.styleName ?? '';

  @override
  OrderState get state => OrderState.fromInt(order.state);

  @override
  TradeInitiator get tradeInitiator =>
      TradeInitiator.fromString(order.quoteInitiator ?? '');

  @override
  List<OrderAction>? get actions => null;

  @override
  List<String> get coverUrls =>
      goods.goodsInfo?.iconUrl != null ? [goods.goodsInfo!.iconUrl!] : [];

  @override
  bool get displayPaymentMethod => false;

  @override
  double? get expiredPrice => null;

  @override
  int get finishedCount => 1;

  @override
  String get name => goods.goodsInfo?.marketName ?? "";

  @override
  String? get stateMessage => order.stateMsg;

  @override
  // int get orderStatusCountdown => order.countdown ?? 0;
  int get orderStatusCountdown => 86419;

  @override
  PaymentMethod get paymentMethod => PaymentMethod.balance;

  @override
  String? get paymentMethodName => null;

  @override
  double get price => order.unitPrice ?? 0;

  @override
  String? get priceTip => null;

  @override
  int get requiredCount => 1;

  @override
  final List<OrderSticker>? stickers;

  @override
  String? get topDateTime => order.purchaseTime;

  @override
  bool get displayTopBar => true;

  @override
  String? get sellerMessage => null;

  @override
  OrderType get type => OrderType.sell;

  bool get isBuyer => UserStore.to.uId == order.buyerUid;

  @override
  bool get enableActions => true;

  @override
  bool get useInlineStateMessage => false;

  @override
  List<AccessoryTagData>? get coverTags =>
      parseCoverTags(goods.goodsInfo?.tags);

  @override
  String get id => order.orderId ?? "NEEDS_ORDER_ID";
}
