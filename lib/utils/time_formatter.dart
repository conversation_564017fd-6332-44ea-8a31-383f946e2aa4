import 'package:intl/intl.dart';

/// 2024-01-01类型的格式化
final ymdDateFormatter = DateFormat('yyyy-MM-dd');

/// 自适应时分秒的格式化。天和小时自动添加，最短也会显示小时或分钟
/// [num] 秒数
String autoFitFormatter(int num) {
  // 计算天数
  final days = num ~/ (24 * 3600);
  // 计算小时
  final hours = (num % (24 * 3600)) ~/ 3600;
  // 计算分钟
  final minutes = (num % 3600) ~/ 60;
  // 计算剩余的秒数
  final secs = num % 60;

  var result = "${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}";
  if (days > 0) {
    result = "$days天 ${hours.toString().padLeft(2, '0')}:$result";
  } else if (hours > 0) {
    result = "${hours.toString().padLeft(2, '0')}:$result";
  }
  return result;
}
